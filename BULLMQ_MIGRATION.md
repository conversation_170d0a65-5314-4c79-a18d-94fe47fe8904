# BullMQ Auto Action Migration

## Overview

This document describes the migration from the unreliable cron-based auto action system to a robust BullMQ-based queue system for handling auto work and auto war functionality.

## Problems with the Previous System

1. **Memory-based storage**: CronJob instances were stored in memory and lost on server restart
2. **Unreliable energy consumption**: Energy calculations were inconsistent
3. **No persistence**: Auto actions didn't survive server restarts
4. **Complex state management**: Used Map-based storage that was hard to debug
5. **No retry mechanism**: Failed actions had no recovery strategy

## New BullMQ Implementation

### Architecture

- **Queue**: `auto-actions` - Single queue for all auto actions
- **Job naming**: `${userId}_${targetId}_${mode}` - Unique job identifiers
- **Job data**: `{ userId, targetId, type }` - Minimal job payload
- **Scheduling**: Every 30 minutes (matching energy regeneration)
- **Persistence**: Redis-backed job storage

### Key Components

#### 1. Queue Module (`src/queue/`)
- `queue.module.ts` - BullMQ configuration and module setup
- `auto-action.queue.ts` - Queue service for managing jobs
- `auto-action.worker.ts` - Worker that processes jobs
- `dto/auto-action-job.dto.ts` - TypeScript interfaces

#### 2. Updated Services
- `BullMQAutoActionService` - New service replacing `AutoActionService`
- `WorkAutoService` - Updated to use BullMQ
- `WarAutoService` - Updated to use BullMQ

### Features

#### Job Management
- **Add jobs**: Automatically scheduled repeating jobs
- **Remove jobs**: Clean removal of jobs and database state
- **Job existence check**: Prevent duplicate jobs
- **Queue statistics**: Monitor job status and performance

#### Worker Logic
- **Energy validation**: Checks minimum energy threshold (10 energy)
- **User validation**: Verifies user exists and is premium
- **Auto mode validation**: Ensures auto mode is still active
- **Expiration handling**: Automatically stops expired work auto mode
- **War status checking**: Stops auto attack when war ends
- **Error handling**: Distinguishes between retryable and critical errors

#### Persistence & Recovery
- **Server restart recovery**: Restores active auto actions on startup
- **Database synchronization**: Keeps user auto mode state in sync
- **Job cleanup**: Removes orphaned jobs and clears invalid states

## Configuration

### Environment Variables
```env
# Redis Configuration for BullMQ
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

### Docker Setup
Redis service added to `docker-compose.yml`:
```yaml
redis:
  image: redis:7-alpine
  container_name: redis
  ports:
    - '6379:6379'
  restart: always
  command: redis-server --appendonly yes
  volumes:
    - redis_data:/data
```

## Migration Benefits

1. **Reliability**: Jobs persist across server restarts
2. **Scalability**: Redis-backed queue can handle high load
3. **Monitoring**: Built-in job status tracking and statistics
4. **Error handling**: Automatic retry with exponential backoff
5. **Consistency**: Unified job processing logic
6. **Debugging**: Better visibility into job execution

## Usage

### Starting Auto Work
```typescript
await workAutoService.startAutoWork(userId, factoryId);
```

### Starting Auto War
```typescript
await warAutoService.startAutoAttack(userId, warId, energyPercentage);
```

### Stopping Auto Actions
```typescript
await workAutoService.stopAutoWork(userId, factoryId);
await warAutoService.stopAutoAttack(userId, warId);
```

## Job Processing Logic

1. **Validation Phase**:
   - Check if user exists and is premium
   - Verify auto mode is still active
   - Check expiration (for work mode)
   - Validate war status (for war mode)

2. **Energy Check**:
   - Calculate current energy using existing logic
   - Skip execution if below minimum threshold (10 energy)

3. **Action Execution**:
   - Execute work at factory or war participation
   - Use all available energy for maximum efficiency

4. **Error Handling**:
   - Retry transient errors (network, temporary failures)
   - Remove jobs for critical errors (target not found, user issues)

## Monitoring

### Queue Statistics
- Waiting jobs count
- Active jobs count
- Completed jobs count
- Failed jobs count
- Repeatable jobs count

### Job Status Tracking
- Individual job success/failure rates
- Processing times
- Error patterns

## Backward Compatibility

The old `AutoActionService` is kept temporarily for backward compatibility, but all new functionality uses `BullMQAutoActionService`. The migration is transparent to the API layer.

## Future Enhancements

1. **Job prioritization**: Different priorities for work vs war
2. **Dynamic scheduling**: Adjust frequency based on user activity
3. **Batch processing**: Group similar actions for efficiency
4. **Advanced monitoring**: Detailed metrics and alerting
5. **Job history**: Long-term job execution analytics
